use derive_builder::Builder;
use crate::meter::durations::{Duration, DurationType};
use crate::note::playback_note::PlaybackNote;
use crate::sequence::note_sequence_trait::{AppendNote, AppendNotes, B<PERSON><PERSON><PERSON>rapper, IterMutWrapper, NextNotes, SetCurPosition};
use crate::sequence::time_note_sequence::{TimeNoteSequence, TimeNoteSequenceBuilder};

#[allow(dead_code)]
#[derive(Builder, Clone, Debug)]
pub(crate) struct FixedTimeNoteSequence {
    #[builder(default)]
    inner_sequence: TimeNoteSequence,
    
    #[builder(default = "DurationType::Quarter")]
    duration_type: DurationType,
    
    #[builder(default = "120", setter(custom))]
    pub(crate) tempo: u8,
    
    #[builder(default = "16")]
    num_steps: usize,
    
    #[builder(default = "0")]
    current_step: usize,
    
    // Computed field based on tempo and duration_type
    #[builder(default = "500.0", setter(skip))]
    step_duration_ms: f32,
}