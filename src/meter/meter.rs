use crate::meter::durations;
use crate::meter::durations::DurationType;

// struct representing a musical meter
pub(crate) struct Meter {
    pub(crate) beats_per_measure: u8,
    pub(crate) beat_unit: durations::DurationType,
    // tempo in beat units per minute
    pub(crate) tempo: u8,
}

impl Meter {
    pub(crate) fn new(beats_per_measure: u8, beat_unit: DurationType, tempo: u8) -> Self {
        Self { beats_per_measure, beat_unit, tempo, }
    }
    
    // return the duration in ms of a note, converted 
    pub(crate) fn beat_duration(&self) -> f32 {
        60000.0 / (self.tempo as f32 * self.beat_unit.to_factor())
    }
    
    // return the duration of a note of a given duration type
    pub(crate) fn note_duration(&self, duration_type: DurationType) -> f32 {
        duration_factor: f32 = duration_type.to_factor() / 
        self.beat_duration() * duration_type.to_factor()
    }
}
